import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/lesson.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class LessonsScreen extends StatefulWidget {
  const LessonsScreen({super.key});

  @override
  State<LessonsScreen> createState() => _LessonsScreenState();
}

class _LessonsScreenState extends State<LessonsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedSubject = '';
  LessonStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cours'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.timetable);
            },
            tooltip: 'Emploi du temps',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Aujourd\'hui', icon: Icon(Icons.today)),
            Tab(text: 'À venir', icon: Icon(Icons.schedule)),
            Tab(text: 'Passés', icon: Icon(Icons.history)),
            Tab(text: 'Tous', icon: Icon(Icons.list)),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildSearchBar(),
              _buildFilterSummary(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildTodayLessons(appProvider),
                    _buildUpcomingLessons(appProvider),
                    _buildPastLessons(appProvider),
                    _buildAllLessons(appProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRouter.addLesson);
        },
        tooltip: 'Ajouter un cours',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Rechercher un cours...',
          prefixIcon: Icon(Icons.search),
          isDense: true,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildFilterSummary() {
    if (_selectedSubject.isEmpty && _selectedStatus == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          const Text('Filtres: '),
          if (_selectedSubject.isNotEmpty) ...[
            Chip(
              label: Text(_selectedSubject),
              onDeleted: () {
                setState(() {
                  _selectedSubject = '';
                });
              },
            ),
            const SizedBox(width: 8),
          ],
          if (_selectedStatus != null) ...[
            Chip(
              label: Text(_getStatusName(_selectedStatus!)),
              onDeleted: () {
                setState(() {
                  _selectedStatus = null;
                });
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTodayLessons(AppProvider appProvider) {
    final lessons = _getFilteredLessons(appProvider.getTodayLessons().cast<Lesson>());
    return _buildLessonsList(lessons, 'Aucun cours prévu aujourd\'hui');
  }

  Widget _buildUpcomingLessons(AppProvider appProvider) {
    final lessons = _getFilteredLessons(appProvider.getUpcomingLessons());
    return _buildLessonsList(lessons, 'Aucun cours à venir');
  }

  Widget _buildPastLessons(AppProvider appProvider) {
    final now = DateTime.now();
    final pastLessons = appProvider.lessons
        .where((lesson) => lesson.endTime.isBefore(now))
        .toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
    
    final lessons = _getFilteredLessons(pastLessons);
    return _buildLessonsList(lessons, 'Aucun cours passé');
  }

  Widget _buildAllLessons(AppProvider appProvider) {
    final allLessons = List<Lesson>.from(appProvider.lessons)
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
    
    final lessons = _getFilteredLessons(allLessons);
    return _buildLessonsList(lessons, 'Aucun cours trouvé');
  }

  List<Lesson> _getFilteredLessons(List<Lesson> lessons) {
    return lessons.where((lesson) {
      final matchesSearch = _searchQuery.isEmpty ||
          lesson.title.toLowerCase().contains(_searchQuery) ||
          lesson.subject.toLowerCase().contains(_searchQuery) ||
          (lesson.description?.toLowerCase().contains(_searchQuery) ?? false);

      final matchesSubject = _selectedSubject.isEmpty ||
          lesson.subject == _selectedSubject;

      final matchesStatus = _selectedStatus == null ||
          lesson.status == _selectedStatus;

      return matchesSearch && matchesSubject && matchesStatus;
    }).toList();
  }

  Widget _buildLessonsList(List<Lesson> lessons, String emptyMessage) {
    if (lessons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: lessons.length,
      itemBuilder: (context, index) {
        final lesson = lessons[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(lesson.status).withValues(alpha: 0.1),
              child: Icon(
                _getStatusIcon(lesson.status),
                color: _getStatusColor(lesson.status),
              ),
            ),
            title: Text(lesson.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(lesson.subject),
                Text(
                  '${DateFormat('dd/MM/yyyy').format(lesson.startTime)} • ${lesson.timeRange}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                if (lesson.classroom != null)
                  Text(
                    'Salle: ${lesson.classroom}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Chip(
                  label: Text(_getStatusName(lesson.status)),
                  backgroundColor: _getStatusColor(lesson.status).withValues(alpha: 0.1),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      Navigator.pushNamed(
                        context,
                        AppRouter.editLesson,
                        arguments: lesson.id,
                      );
                    } else if (value == 'delete') {
                      _showDeleteConfirmation(lesson);
                    } else if (value == 'duplicate') {
                      _duplicateLesson(lesson);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Modifier'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy),
                          SizedBox(width: 8),
                          Text('Dupliquer'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Supprimer', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                AppRouter.lessonDetail,
                arguments: lesson.id,
              );
            },
          ),
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final subjects = appProvider.activeSubjects.map((s) => s.name).toSet().toList();
          
          return AlertDialog(
            title: const Text('Filtrer les cours'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedSubject.isEmpty ? null : _selectedSubject,
                  decoration: const InputDecoration(
                    labelText: 'Matière',
                    isDense: true,
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('Toutes les matières'),
                    ),
                    ...subjects.map((subject) => DropdownMenuItem(
                      value: subject,
                      child: Text(subject),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSubject = value ?? '';
                    });
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<LessonStatus>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Statut',
                    isDense: true,
                  ),
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('Tous les statuts'),
                    ),
                    ...LessonStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(_getStatusName(status)),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedSubject = '';
                    _selectedStatus = null;
                  });
                  Navigator.pop(context);
                },
                child: const Text('Réinitialiser'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation(Lesson lesson) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le cours'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer le cours "${lesson.title}" ? Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteLesson(lesson);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _deleteLesson(Lesson lesson) async {
    try {
      // TODO: Implement lesson deletion in AppProvider
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cours supprimé avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la suppression: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _duplicateLesson(Lesson lesson) {
    // TODO: Implement lesson duplication
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de duplication à implémenter'),
      ),
    );
  }

  Color _getStatusColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return AppTheme.primaryBlue;
      case LessonStatus.inProgress:
        return AppTheme.warningOrange;
      case LessonStatus.completed:
        return AppTheme.successGreen;
      case LessonStatus.cancelled:
        return AppTheme.accentRed;
    }
  }

  IconData _getStatusIcon(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return Icons.schedule;
      case LessonStatus.inProgress:
        return Icons.play_circle;
      case LessonStatus.completed:
        return Icons.check_circle;
      case LessonStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusName(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return 'Planifié';
      case LessonStatus.inProgress:
        return 'En cours';
      case LessonStatus.completed:
        return 'Terminé';
      case LessonStatus.cancelled:
        return 'Annulé';
    }
  }
}
