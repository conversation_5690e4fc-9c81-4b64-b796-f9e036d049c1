import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/settings_service.dart';
import '../services/backup_service.dart';
import '../services/license_service.dart';
import '../models/student.dart';
import '../models/attendance.dart';
import '../models/behavior_note.dart';
import '../models/grade.dart';
import '../models/rubric.dart';
import '../models/lesson.dart';
import '../models/feedback_template.dart';
import '../models/course.dart';
import '../models/app_settings.dart';

class AppProvider extends ChangeNotifier {
  // Settings management
  AppSettings _settings = AppSettings();
  AppSettings get settings => _settings;

  // Theme management
  bool get isDarkMode => _settings.isDarkMode;

  void toggleTheme() {
    final newValue = !_settings.isDarkMode;
    _settings = _settings.copyWith(isDarkMode: newValue);
    SettingsService.updateSetting('isDarkMode', newValue);
    notifyListeners();
  }

  void setTheme(bool isDark) {
    _settings = _settings.copyWith(isDarkMode: isDark);
    SettingsService.updateSetting('isDarkMode', isDark);
    notifyListeners();
  }

  // License management
  bool get isLicensed => LicenseService.isLicensed;
  String get licenseStatus => LicenseService.licenseStatusText;

  // Settings management methods
  Future<void> loadSettings() async {
    _settings = SettingsService.currentSettings;
    notifyListeners();
  }

  Future<void> updateSetting(String key, dynamic value) async {
    await SettingsService.updateSetting(key, value);
    _settings = SettingsService.currentSettings;
    notifyListeners();
  }

  // Check if feature is restricted
  bool isFeatureRestricted(String featureName) {
    return LicenseService.isFeatureRestricted(featureName);
  }

  // Show restriction dialog
  void showRestrictionDialog(BuildContext context, String featureName) {
    LicenseService.showRestrictionDialog(context, featureName);
  }

  // Loading states
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Students management
  List<Student> _students = [];
  List<Student> get students => _students;
  List<Student> get activeStudents =>
      _students.where((s) => s.isActive).toList();

  Future<void> loadStudents() async {
    setLoading(true);
    try {
      _students = DatabaseService.allStudents;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading students: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> addStudent(Student student) async {
    try {
      await DatabaseService.saveStudent(student);
      _students.add(student);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding student: $e');
      rethrow;
    }
  }

  Future<void> updateStudent(Student student) async {
    try {
      await DatabaseService.saveStudent(student);
      final index = _students.indexWhere((s) => s.id == student.id);
      if (index != -1) {
        _students[index] = student;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating student: $e');
      rethrow;
    }
  }

  Future<void> deleteStudent(String studentId) async {
    try {
      await DatabaseService.deleteStudent(studentId);
      final index = _students.indexWhere((s) => s.id == studentId);
      if (index != -1) {
        _students[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting student: $e');
      rethrow;
    }
  }

  Student? getStudent(String studentId) {
    try {
      return _students.firstWhere((s) => s.id == studentId);
    } catch (e) {
      return null;
    }
  }

  // Attendance management
  final Map<String, DailyAttendance> _dailyAttendanceCache = {};

  Future<DailyAttendance?> getDailyAttendance(DateTime date) async {
    final dateKey =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

    if (_dailyAttendanceCache.containsKey(dateKey)) {
      return _dailyAttendanceCache[dateKey];
    }

    final dailyAttendance = DatabaseService.getDailyAttendance(date);
    if (dailyAttendance != null) {
      _dailyAttendanceCache[dateKey] = dailyAttendance;
    }

    return dailyAttendance;
  }

  Future<DailyAttendance> getOrCreateDailyAttendance(DateTime date) async {
    var dailyAttendance = await getDailyAttendance(date);

    if (dailyAttendance == null) {
      dailyAttendance = DailyAttendance(date: date);
      await DatabaseService.saveDailyAttendance(dailyAttendance);

      final dateKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      _dailyAttendanceCache[dateKey] = dailyAttendance;
    }

    return dailyAttendance;
  }

  Future<void> updateAttendance(
    String studentId,
    DateTime date,
    AttendanceStatus status, {
    String? notes,
  }) async {
    try {
      final dailyAttendance = await getOrCreateDailyAttendance(date);
      dailyAttendance.updateRecord(studentId, status, notes: notes);
      await DatabaseService.saveDailyAttendance(dailyAttendance);
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating attendance: $e');
      rethrow;
    }
  }

  Future<void> saveDailyAttendance(DailyAttendance dailyAttendance) async {
    try {
      await DatabaseService.saveDailyAttendance(dailyAttendance);
      final dateKey =
          '${dailyAttendance.date.year}-${dailyAttendance.date.month.toString().padLeft(2, '0')}-${dailyAttendance.date.day.toString().padLeft(2, '0')}';
      _dailyAttendanceCache[dateKey] = dailyAttendance;
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving daily attendance: $e');
      rethrow;
    }
  }

  // Behavior notes management
  List<BehaviorNote> _behaviorNotes = [];
  List<BehaviorNote> get behaviorNotes => _behaviorNotes;

  Future<void> loadBehaviorNotes() async {
    try {
      _behaviorNotes = DatabaseService.behaviorNotesBox.values.toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading behavior notes: $e');
    }
  }

  Future<void> addBehaviorNote(BehaviorNote note) async {
    try {
      await DatabaseService.saveBehaviorNote(note);
      _behaviorNotes.add(note);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding behavior note: $e');
      rethrow;
    }
  }

  List<BehaviorNote> getStudentBehaviorNotes(String studentId) {
    return _behaviorNotes.where((note) => note.studentId == studentId).toList();
  }

  Future<void> updateBehaviorNote(BehaviorNote note) async {
    try {
      await DatabaseService.saveBehaviorNote(note);
      final index = _behaviorNotes.indexWhere((n) => n.id == note.id);
      if (index != -1) {
        _behaviorNotes[index] = note;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating behavior note: $e');
      rethrow;
    }
  }

  Future<void> deleteBehaviorNote(String noteId) async {
    try {
      await DatabaseService.deleteBehaviorNote(noteId);
      _behaviorNotes.removeWhere((note) => note.id == noteId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting behavior note: $e');
      rethrow;
    }
  }

  // Grades management
  List<Grade> _grades = [];
  List<Grade> get grades => _grades;

  Future<void> loadGrades() async {
    try {
      _grades = DatabaseService.gradesBox.values.toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading grades: $e');
    }
  }

  Future<void> addGrade(Grade grade) async {
    try {
      await DatabaseService.saveGrade(grade);
      _grades.add(grade);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding grade: $e');
      rethrow;
    }
  }

  List<Grade> getStudentGrades(String studentId) {
    return _grades.where((grade) => grade.studentId == studentId).toList();
  }

  List<Grade> getStudentGradesBySubject(String studentId, String subject) {
    return _grades
        .where(
          (grade) => grade.studentId == studentId && grade.subject == subject,
        )
        .toList();
  }

  double calculateStudentAverage(String studentId, String subject) {
    final studentGrades = getStudentGradesBySubject(studentId, subject);
    if (studentGrades.isEmpty) return 0.0;

    double totalWeightedGrades = 0.0;
    double totalCoefficients = 0.0;

    for (final grade in studentGrades) {
      totalWeightedGrades += grade.normalizedValue * grade.coefficient;
      totalCoefficients += grade.coefficient;
    }

    return totalCoefficients > 0
        ? totalWeightedGrades / totalCoefficients
        : 0.0;
  }

  Future<void> updateGrade(Grade grade) async {
    try {
      await DatabaseService.saveGrade(grade);
      final index = _grades.indexWhere((g) => g.id == grade.id);
      if (index != -1) {
        _grades[index] = grade;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating grade: $e');
      rethrow;
    }
  }

  Future<void> deleteGrade(String gradeId) async {
    try {
      await DatabaseService.deleteGrade(gradeId);
      _grades.removeWhere((grade) => grade.id == gradeId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting grade: $e');
      rethrow;
    }
  }

  // Dashboard data methods
  Map<String, dynamic> getDashboardData() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return {
      'totalStudents': _students.where((s) => s.isActive).length,
      'todayLessons': _lessons
          .where(
            (lesson) =>
                lesson.startTime.isAfter(today) &&
                lesson.startTime.isBefore(tomorrow),
          )
          .length,
      'recentGrades': _grades
          .where(
            (grade) =>
                grade.createdAt.isAfter(now.subtract(const Duration(days: 7))),
          )
          .length,
      'recentBehaviorNotes': _behaviorNotes
          .where(
            (note) =>
                note.createdAt.isAfter(now.subtract(const Duration(days: 7))),
          )
          .length,
    };
  }

  List<dynamic> getTodayLessons() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return _lessons
        .where(
          (lesson) =>
              lesson.startTime.isAfter(today) &&
              lesson.startTime.isBefore(tomorrow),
        )
        .toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  // Lessons management
  List<Lesson> _lessons = [];
  List<Lesson> get lessons => _lessons;

  Future<void> loadLessons() async {
    try {
      _lessons = DatabaseService.allLessons;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading lessons: $e');
    }
  }

  Future<void> addLesson(Lesson lesson) async {
    try {
      await DatabaseService.saveLesson(lesson);
      _lessons.add(lesson);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding lesson: $e');
      rethrow;
    }
  }

  Future<void> updateLesson(Lesson lesson) async {
    try {
      await DatabaseService.saveLesson(lesson);
      final index = _lessons.indexWhere((l) => l.id == lesson.id);
      if (index != -1) {
        _lessons[index] = lesson;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating lesson: $e');
      rethrow;
    }
  }

  List<Lesson> getLessonsForDate(DateTime date) {
    return _lessons
        .where(
          (lesson) =>
              lesson.startTime.year == date.year &&
              lesson.startTime.month == date.month &&
              lesson.startTime.day == date.day,
        )
        .toList();
  }

  // getTodayLessons method moved above

  List<Lesson> getUpcomingLessons() {
    final now = DateTime.now();
    return _lessons.where((lesson) => lesson.startTime.isAfter(now)).toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  // Rubrics management
  List<Rubric> _rubrics = [];
  List<Rubric> get rubrics => _rubrics;

  Future<void> loadRubrics() async {
    try {
      _rubrics = DatabaseService.allRubrics;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading rubrics: $e');
    }
  }

  Future<void> addRubric(Rubric rubric) async {
    try {
      await DatabaseService.saveRubric(rubric);
      _rubrics.add(rubric);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding rubric: $e');
      rethrow;
    }
  }

  // Feedback templates management
  List<FeedbackTemplate> _feedbackTemplates = [];
  List<FeedbackTemplate> get feedbackTemplates => _feedbackTemplates;

  Future<void> loadFeedbackTemplates() async {
    try {
      _feedbackTemplates = DatabaseService.activeFeedbackTemplates;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading feedback templates: $e');
    }
  }

  List<FeedbackTemplate> getFeedbackTemplatesByCategory(
    FeedbackCategory category,
  ) {
    return _feedbackTemplates
        .where((template) => template.category == category)
        .toList();
  }

  // Subjects management
  List<Subject> _subjects = [];
  List<Subject> get subjects => _subjects;
  List<Subject> get activeSubjects =>
      _subjects.where((s) => s.isActive).toList();

  Future<void> loadSubjects() async {
    try {
      _subjects = DatabaseService.allSubjects;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading subjects: $e');
    }
  }

  Future<void> addSubject(Subject subject) async {
    try {
      await DatabaseService.saveSubject(subject);
      _subjects.add(subject);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding subject: $e');
      rethrow;
    }
  }

  Future<void> updateSubject(Subject subject) async {
    try {
      await DatabaseService.saveSubject(subject);
      final index = _subjects.indexWhere((s) => s.id == subject.id);
      if (index != -1) {
        _subjects[index] = subject;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating subject: $e');
      rethrow;
    }
  }

  Future<void> deleteSubject(String subjectId) async {
    try {
      await DatabaseService.deleteSubject(subjectId);
      final index = _subjects.indexWhere((s) => s.id == subjectId);
      if (index != -1) {
        _subjects[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting subject: $e');
      rethrow;
    }
  }

  Subject? getSubject(String subjectId) {
    try {
      return _subjects.firstWhere((s) => s.id == subjectId);
    } catch (e) {
      return null;
    }
  }

  List<Subject> getSubjectsByCategory(CourseCategory category) {
    return _subjects
        .where((subject) => subject.category == category && subject.isActive)
        .toList();
  }

  // Courses management
  List<Course> _courses = [];
  List<Course> get courses => _courses;
  List<Course> get activeCourses => _courses.where((c) => c.isActive).toList();

  Future<void> loadCourses() async {
    try {
      _courses = DatabaseService.allCourses;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading courses: $e');
    }
  }

  Future<void> addCourse(Course course) async {
    try {
      await DatabaseService.saveCourse(course);
      _courses.add(course);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding course: $e');
      rethrow;
    }
  }

  Future<void> updateCourse(Course course) async {
    try {
      await DatabaseService.saveCourse(course);
      final index = _courses.indexWhere((c) => c.id == course.id);
      if (index != -1) {
        _courses[index] = course;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating course: $e');
      rethrow;
    }
  }

  Future<void> deleteCourse(String courseId) async {
    try {
      await DatabaseService.deleteCourse(courseId);
      final index = _courses.indexWhere((c) => c.id == courseId);
      if (index != -1) {
        _courses[index].isActive = false;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error deleting course: $e');
      rethrow;
    }
  }

  Course? getCourse(String courseId) {
    try {
      return _courses.firstWhere((c) => c.id == courseId);
    } catch (e) {
      return null;
    }
  }

  List<Course> getCoursesBySubject(String subjectId) {
    return _courses
        .where((course) => course.subjectId == subjectId && course.isActive)
        .toList();
  }

  List<Course> getCoursesByLevel(EducationLevel level) {
    return _courses
        .where((course) => course.level == level && course.isActive)
        .toList();
  }

  // getDashboardData method moved above

  // Initialize all data
  Future<void> initializeApp() async {
    setLoading(true);
    try {
      // Initialize services first
      await SettingsService.init();
      await LicenseService.init();

      // Load settings
      await loadSettings();

      // Load all data
      await Future.wait([
        loadStudents(),
        loadBehaviorNotes(),
        loadGrades(),
        loadLessons(),
        loadRubrics(),
        loadFeedbackTemplates(),
        loadSubjects(),
        loadCourses(),
      ]);

      // Perform auto-backup if enabled
      BackupService.performAutoBackup();
    } catch (e) {
      debugPrint('Error initializing app: $e');
    } finally {
      setLoading(false);
    }
  }

  // Clear cache
  void clearCache() {
    _dailyAttendanceCache.clear();
  }
}
