import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/lesson.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class LessonDetailScreen extends StatefulWidget {
  final String lessonId;

  const LessonDetailScreen({super.key, required this.lessonId});

  @override
  State<LessonDetailScreen> createState() => _LessonDetailScreenState();
}

class _LessonDetailScreenState extends State<LessonDetailScreen> {
  Lesson? _lesson;

  @override
  void initState() {
    super.initState();
    _loadLesson();
  }

  void _loadLesson() {
    final appProvider = context.read<AppProvider>();
    _lesson = appProvider.lessons.firstWhere(
      (lesson) => lesson.id == widget.lessonId,
      orElse: () => throw Exception('Lesson not found'),
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_lesson == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Cours')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_lesson!.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRouter.editLesson,
                arguments: _lesson!.id,
              );
            },
            tooltip: 'Modifier',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'duplicate') {
                _duplicateLesson();
              } else if (value == 'delete') {
                _showDeleteConfirmation();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Dupliquer'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Supprimer', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderCard(),
            const SizedBox(height: 16),
            _buildScheduleCard(),
            const SizedBox(height: 16),
            if (_lesson!.description != null) ...[
              _buildDescriptionCard(),
              const SizedBox(height: 16),
            ],
            if (_lesson!.objectives != null &&
                _lesson!.objectives!.isNotEmpty) ...[
              _buildObjectivesCard(),
              const SizedBox(height: 16),
            ],
            if (_lesson!.content != null) ...[
              _buildContentCard(),
              const SizedBox(height: 16),
            ],
            if (_lesson!.homework != null) ...[
              _buildHomeworkCard(),
              const SizedBox(height: 16),
            ],
            if (_lesson!.studentIds != null &&
                _lesson!.studentIds!.isNotEmpty) ...[
              _buildStudentsCard(),
              const SizedBox(height: 16),
            ],
            if (_lesson!.notes != null) ...[
              _buildNotesCard(),
              const SizedBox(height: 16),
            ],
            _buildStatusCard(),
          ],
        ),
      ),
      floatingActionButton: _buildActionButton(),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(
                    _lesson!.status,
                  ).withValues(alpha: 0.1),
                  child: Icon(
                    _getStatusIcon(_lesson!.status),
                    color: _getStatusColor(_lesson!.status),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _lesson!.title,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      Text(
                        _lesson!.subject,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: AppTheme.primaryBlue),
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: Text(_getStatusName(_lesson!.status)),
                  backgroundColor: _getStatusColor(
                    _lesson!.status,
                  ).withValues(alpha: 0.1),
                ),
              ],
            ),
            if (_lesson!.classroom != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 20),
                  const SizedBox(width: 8),
                  Text('Salle: ${_lesson!.classroom}'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Horaires', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 20),
                const SizedBox(width: 8),
                Text(DateFormat('EEEE d MMMM yyyy').format(_lesson!.startTime)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.access_time, size: 20),
                const SizedBox(width: 8),
                Text(_lesson!.timeRange),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.timer, size: 20),
                const SizedBox(width: 8),
                Text('Durée: ${_lesson!.duration.inMinutes} minutes'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Description', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(_lesson!.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectivesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Objectifs pédagogiques',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            ...List.generate(_lesson!.objectives!.length, (index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(_lesson!.objectives![index])),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contenu du cours',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(_lesson!.content!),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeworkCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Devoirs', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Text(_lesson!.homework!),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Élèves participants',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text('${_lesson!.studentIds!.length} élève(s) participant(s)'),
            // TODO: Display actual student names
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes personnelles',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(_lesson!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Statut', _getStatusName(_lesson!.status)),
            _buildDetailRow(
              'Créé le',
              DateFormat('dd/MM/yyyy à HH:mm').format(_lesson!.createdAt),
            ),
            _buildDetailRow(
              'Modifié le',
              DateFormat('dd/MM/yyyy à HH:mm').format(_lesson!.updatedAt),
            ),
            if (_lesson!.isRecurring) ...[
              _buildDetailRow('Récurrence', _lesson!.recurringPattern ?? 'Oui'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget? _buildActionButton() {
    if (_lesson!.status == LessonStatus.planned) {
      return FloatingActionButton.extended(
        onPressed: _startLesson,
        icon: const Icon(Icons.play_arrow),
        label: const Text('Commencer'),
      );
    } else if (_lesson!.status == LessonStatus.inProgress) {
      return FloatingActionButton.extended(
        onPressed: _endLesson,
        icon: const Icon(Icons.stop),
        label: const Text('Terminer'),
      );
    }
    return null;
  }

  void _startLesson() {
    // TODO: Implement lesson start functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de démarrage à implémenter'),
      ),
    );
  }

  void _endLesson() {
    // TODO: Implement lesson end functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de fin de cours à implémenter'),
      ),
    );
  }

  void _duplicateLesson() {
    // TODO: Implement lesson duplication
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité de duplication à implémenter'),
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le cours'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer le cours "${_lesson!.title}" ? Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteLesson();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _deleteLesson() async {
    try {
      // TODO: Implement lesson deletion in AppProvider
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cours supprimé avec succès'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la suppression: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Color _getStatusColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return AppTheme.primaryBlue;
      case LessonStatus.inProgress:
        return AppTheme.warningOrange;
      case LessonStatus.completed:
        return AppTheme.successGreen;
      case LessonStatus.cancelled:
        return AppTheme.accentRed;
    }
  }

  IconData _getStatusIcon(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return Icons.schedule;
      case LessonStatus.inProgress:
        return Icons.play_circle;
      case LessonStatus.completed:
        return Icons.check_circle;
      case LessonStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusName(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return 'Planifié';
      case LessonStatus.inProgress:
        return 'En cours';
      case LessonStatus.completed:
        return 'Terminé';
      case LessonStatus.cancelled:
        return 'Annulé';
    }
  }
}
